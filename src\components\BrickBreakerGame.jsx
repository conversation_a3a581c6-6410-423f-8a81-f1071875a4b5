import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Stage, Layer, Rect, Circle, Text } from 'react-konva';
import HighScoreInput from './HighScoreInput';
import HighScoreBoard from './HighScoreBoard';

const BrickBreakerGame = () => {
  const stageRef = useRef();
  const animationRef = useRef();
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  // Initial game dimensions
  const getInitialDimensions = () => {
    const isMobileDevice = window.innerWidth <= 768;
    if (isMobileDevice) {
      const maxWidth = Math.min(window.innerWidth - 40, 400);
      return {
        width: maxWidth,
        height: Math.floor(maxWidth * 1.2) // Taller aspect ratio for mobile (5:6)
      };
    }
    return { width: 800, height: 700 }; // Increased desktop height
  };

  const [gameDimensions, setGameDimensions] = useState(getInitialDimensions);
  const GAME_WIDTH = gameDimensions.width;
  const GAME_HEIGHT = gameDimensions.height;
  // Responsive game constants
  const gameConstants = useMemo(() => {
    const PADDLE_WIDTH = isMobile ? Math.max(60, GAME_WIDTH * 0.15) : 100;
    const PADDLE_HEIGHT = isMobile ? 12 : 15;
    const BALL_RADIUS = isMobile ? 6 : 8;
    const BRICK_COLS = isMobile ? 6 : 10;
    const BRICK_PADDING = isMobile ? 3 : 5;
    // Better brick width calculation to prevent cutoff
    const totalPadding = BRICK_PADDING * (BRICK_COLS + 1);
    const availableWidth = GAME_WIDTH - totalPadding;
    const BRICK_WIDTH = Math.floor(availableWidth / BRICK_COLS);
    const BRICK_HEIGHT = isMobile ? 15 : 20;
    const BRICK_ROWS = 6;

    return {
      PADDLE_WIDTH,
      PADDLE_HEIGHT,
      BALL_RADIUS,
      BRICK_COLS,
      BRICK_WIDTH,
      BRICK_HEIGHT,
      BRICK_ROWS,
      BRICK_PADDING
    };
  }, [isMobile, GAME_WIDTH]);

  const {
    PADDLE_WIDTH,
    PADDLE_HEIGHT,
    BALL_RADIUS,
    BRICK_COLS,
    BRICK_WIDTH,
    BRICK_HEIGHT,
    BRICK_ROWS,
    BRICK_PADDING
  } = gameConstants;

  // Game state
  const [gameState, setGameState] = useState('waiting'); // 'waiting', 'playing', 'paused', 'gameOver', 'won', 'levelComplete'
  const [score, setScore] = useState(0);
  const [lives, setLives] = useState(3);
  const [level, setLevel] = useState(1);
  const [ballSpeed, setBallSpeed] = useState(4);

  // High score system state
  const [showHighScoreInput, setShowHighScoreInput] = useState(false);
  const [showHighScoreBoard, setShowHighScoreBoard] = useState(false);
  const [isNewHighScore, setIsNewHighScore] = useState(false);

  // Game objects with dynamic positioning
  const [paddle, setPaddle] = useState(() => ({
    x: GAME_WIDTH / 2 - PADDLE_WIDTH / 2,
    y: GAME_HEIGHT - (isMobile ? 40 : 60),
    width: PADDLE_WIDTH,
    height: PADDLE_HEIGHT
  }));

  const [ball, setBall] = useState(() => ({
    x: GAME_WIDTH / 2,
    y: GAME_HEIGHT - (isMobile ? 60 : 80),
    radius: BALL_RADIUS,
    dx: 0,
    dy: 0
  }));

  const [bricks, setBricks] = useState([]);

  // Power-up system
  const [powerUps, setPowerUps] = useState([]);
  const [activePowerUps, setActivePowerUps] = useState({});
  const [blasterShots, setBlasterShots] = useState([]);

  // Power-up types
  const powerUpTypes = {
    BLASTER: {
      name: 'Blaster',
      color: '#FF4444',
      symbol: '🔫',
      duration: 15000, // 15 seconds
      description: 'Shoot projectiles with spacebar!'
    },
    MULTI_BALL: {
      name: 'Multi Ball',
      color: '#44FF44',
      symbol: '⚽',
      duration: 0, // Instant effect
      description: 'Extra balls!'
    },
    BIG_PADDLE: {
      name: 'Big Paddle',
      color: '#4444FF',
      symbol: '📏',
      duration: 20000, // 20 seconds
      description: 'Bigger paddle!'
    },
    SLOW_BALL: {
      name: 'Slow Ball',
      color: '#FFFF44',
      symbol: '🐌',
      duration: 15000, // 15 seconds
      description: 'Slower ball speed!'
    },
    EXTRA_LIFE: {
      name: 'Extra Life',
      color: '#FF44FF',
      symbol: '❤️',
      duration: 0, // Instant effect
      description: 'Extra life!'
    }
  };

  // Level configurations
  const levelConfigs = {
    1: {
      rows: 4,
      pattern: 'full',
      colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
      ballSpeed: 4,
      pointsPerBrick: 10
    },
    2: {
      rows: 5,
      pattern: 'checkerboard',
      colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
      ballSpeed: 4.5,
      pointsPerBrick: 15
    },
    3: {
      rows: 6,
      pattern: 'pyramid',
      colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'],
      ballSpeed: 5,
      pointsPerBrick: 20
    },
    4: {
      rows: 6,
      pattern: 'diamond',
      colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'],
      ballSpeed: 5.5,
      pointsPerBrick: 25
    },
    5: {
      rows: 7,
      pattern: 'full',
      colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#FFB6C1'],
      ballSpeed: 6,
      pointsPerBrick: 30
    },
    6: {
      rows: 6,
      pattern: 'cross',
      colors: ['#E74C3C', '#F39C12', '#F1C40F', '#27AE60', '#3498DB', '#9B59B6'],
      ballSpeed: 6.2,
      pointsPerBrick: 35
    },
    7: {
      rows: 7,
      pattern: 'zigzag',
      colors: ['#E74C3C', '#F39C12', '#F1C40F', '#27AE60', '#3498DB', '#9B59B6', '#E67E22'],
      ballSpeed: 6.5,
      pointsPerBrick: 40
    },
    8: {
      rows: 8,
      pattern: 'spiral',
      colors: ['#C0392B', '#D35400', '#F39C12', '#F1C40F', '#27AE60', '#16A085', '#2980B9', '#8E44AD'],
      ballSpeed: 6.8,
      pointsPerBrick: 45
    },
    9: {
      rows: 6,
      pattern: 'heart',
      colors: ['#E91E63', '#F06292', '#F8BBD9', '#FCE4EC', '#F8BBD9', '#F06292'],
      ballSpeed: 7,
      pointsPerBrick: 50
    },
    10: {
      rows: 8,
      pattern: 'maze',
      colors: ['#795548', '#8D6E63', '#A1887F', '#BCAAA4', '#D7CCC8', '#EFEBE9', '#D7CCC8', '#BCAAA4'],
      ballSpeed: 7.2,
      pointsPerBrick: 55
    },
    11: {
      rows: 7,
      pattern: 'wave',
      colors: ['#006064', '#00838F', '#0097A7', '#00ACC1', '#26C6DA', '#4DD0E1', '#80DEEA'],
      ballSpeed: 7.5,
      pointsPerBrick: 60
    },
    12: {
      rows: 9,
      pattern: 'fortress',
      colors: ['#263238', '#37474F', '#455A64', '#546E7A', '#607D8B', '#78909C', '#90A4AE', '#B0BEC5', '#CFD8DC'],
      ballSpeed: 7.8,
      pointsPerBrick: 65
    },
    13: {
      rows: 8,
      pattern: 'star',
      colors: ['#FFD700', '#FFC107', '#FF9800', '#FF5722', '#F44336', '#E91E63', '#9C27B0', '#673AB7'],
      ballSpeed: 8,
      pointsPerBrick: 70
    },
    14: {
      rows: 10,
      pattern: 'rainbow',
      colors: ['#F44336', '#FF9800', '#FFEB3B', '#4CAF50', '#2196F3', '#9C27B0', '#E91E63', '#FF5722', '#795548', '#607D8B'],
      ballSpeed: 8.5,
      pointsPerBrick: 75
    },
    15: {
      rows: 12,
      pattern: 'ultimate',
      colors: ['#B71C1C', '#E65100', '#F57F17', '#33691E', '#1A237E', '#4A148C', '#880E4F', '#BF360C', '#3E2723', '#263238', '#1B5E20', '#0D47A1'],
      ballSpeed: 9,
      pointsPerBrick: 100
    }
  };

  // Generate power-up when brick is destroyed
  const generatePowerUp = (brickX, brickY) => {
    // 8% chance to drop a power-up (reduced from 20%)
    if (Math.random() < 0.08) {
      const powerUpTypeKeys = Object.keys(powerUpTypes);
      const randomType = powerUpTypeKeys[Math.floor(Math.random() * powerUpTypeKeys.length)];

      return {
        id: Date.now() + Math.random(),
        type: randomType,
        x: brickX + BRICK_WIDTH / 2 - 15, // Center on brick
        y: brickY,
        width: 30,
        height: 20,
        dy: 2, // Fall speed
        collected: false
      };
    }
    return null;
  };

  // Initialize bricks based on level
  const initializeBricks = useCallback(() => {
    const config = levelConfigs[level] || levelConfigs[15]; // Default to level 15 for higher levels
    const newBricks = [];

    for (let row = 0; row < config.rows; row++) {
      for (let col = 0; col < BRICK_COLS; col++) {
        let shouldCreateBrick = true;

        // Apply pattern logic
        switch (config.pattern) {
          case 'checkerboard':
            shouldCreateBrick = (row + col) % 2 === 0;
            break;
          case 'pyramid':
            const centerCol = BRICK_COLS / 2;
            const maxDistance = Math.min(row + 1, BRICK_COLS / 2);
            shouldCreateBrick = Math.abs(col - centerCol + 0.5) < maxDistance;
            break;
          case 'diamond':
            const center = BRICK_COLS / 2;
            const diamondSize = Math.min(3, config.rows - Math.abs(row - config.rows / 2));
            shouldCreateBrick = Math.abs(col - center + 0.5) < diamondSize;
            break;
          case 'cross':
            const midRow = Math.floor(config.rows / 2);
            const midCol = Math.floor(BRICK_COLS / 2);
            shouldCreateBrick = (row === midRow) || (col === midCol) || (col === midCol - 1);
            break;
          case 'zigzag':
            shouldCreateBrick = (row % 2 === 0) ? (col % 3 === 0) : (col % 3 === 1);
            break;
          case 'spiral':
            const spiralCenter = Math.floor(BRICK_COLS / 2);
            const distance = Math.abs(col - spiralCenter) + Math.abs(row - Math.floor(config.rows / 2));
            shouldCreateBrick = distance <= 3 && (row + col) % 2 === 0;
            break;
          case 'heart':
            const heartMid = Math.floor(BRICK_COLS / 2);
            if (row < 2) {
              shouldCreateBrick = (col >= heartMid - 2 && col <= heartMid - 1) || (col >= heartMid + 1 && col <= heartMid + 2);
            } else {
              const heartWidth = Math.max(1, 4 - (row - 1));
              shouldCreateBrick = Math.abs(col - heartMid + 0.5) < heartWidth;
            }
            break;
          case 'maze':
            shouldCreateBrick = !((row % 2 === 1 && col % 2 === 1) ||
              (row % 4 === 0 && col % 3 === 1) ||
              (row % 3 === 2 && col % 4 === 2));
            break;
          case 'wave':
            const waveOffset = Math.sin((col / BRICK_COLS) * Math.PI * 2) * 2;
            shouldCreateBrick = Math.abs(row - (config.rows / 2 + waveOffset)) < 2;
            break;
          case 'fortress':
            const fortressEdge = col === 0 || col === BRICK_COLS - 1 || row === 0 || row === config.rows - 1;
            const fortressGate = (row > config.rows / 2) && (col >= BRICK_COLS / 2 - 1) && (col <= BRICK_COLS / 2);
            shouldCreateBrick = fortressEdge && !fortressGate;
            break;
          case 'star':
            const starCenter = { x: BRICK_COLS / 2, y: config.rows / 2 };
            const angle = Math.atan2(row - starCenter.y, col - starCenter.x);
            const starRadius = Math.sqrt((col - starCenter.x) ** 2 + (row - starCenter.y) ** 2);
            const starPattern = Math.sin(angle * 5) > 0;
            shouldCreateBrick = starRadius < 4 && starPattern;
            break;
          case 'rainbow':
            shouldCreateBrick = true; // Full pattern with rainbow colors
            break;
          case 'ultimate':
            const ultCenter = { x: BRICK_COLS / 2, y: config.rows / 2 };
            const ultDistance = Math.sqrt((col - ultCenter.x) ** 2 + (row - ultCenter.y) ** 2);
            const ultPattern = (row + col) % 3 !== 0 && ultDistance < 6;
            shouldCreateBrick = ultPattern;
            break;
          case 'full':
          default:
            shouldCreateBrick = true;
            break;
        }

        if (shouldCreateBrick) {
          newBricks.push({
            x: col * (BRICK_WIDTH + BRICK_PADDING) + BRICK_PADDING,
            y: row * (BRICK_HEIGHT + BRICK_PADDING) + 50,
            width: BRICK_WIDTH,
            height: BRICK_HEIGHT,
            color: config.colors[row % config.colors.length],
            destroyed: false,
            id: `${row}-${col}`,
            points: config.pointsPerBrick
          });
        }
      }
    }
    setBricks(newBricks);
    setBallSpeed(config.ballSpeed);
  }, [level, BRICK_COLS, BRICK_WIDTH, BRICK_HEIGHT, BRICK_PADDING]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      const isMobileDevice = window.innerWidth <= 768;
      setIsMobile(isMobileDevice);

      const newDimensions = isMobileDevice
        ? {
          width: Math.min(window.innerWidth - 40, 400),
          height: Math.floor(Math.min(window.innerWidth - 40, 400) * 1.2)
        }
        : { width: 800, height: 700 };

      setGameDimensions(newDimensions);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Update paddle and ball positions when dimensions change
  useEffect(() => {
    if (gameState === 'waiting') {
      setPaddle(prev => ({
        ...prev,
        x: GAME_WIDTH / 2 - PADDLE_WIDTH / 2,
        y: GAME_HEIGHT - (isMobile ? 40 : 60),
        width: PADDLE_WIDTH,
        height: PADDLE_HEIGHT
      }));
      setBall(prev => ({
        ...prev,
        x: GAME_WIDTH / 2,
        y: GAME_HEIGHT - (isMobile ? 60 : 80),
        radius: BALL_RADIUS
      }));
    }
  }, [GAME_WIDTH, GAME_HEIGHT, PADDLE_WIDTH, PADDLE_HEIGHT, BALL_RADIUS, isMobile, gameState]);

  // Initialize game
  useEffect(() => {
    initializeBricks();
  }, [initializeBricks]);

  // Start game
  const startGame = () => {
    const angle = (Math.random() - 0.5) * 0.8; // Random angle
    setBall(prev => ({
      ...prev,
      dx: ballSpeed * Math.sin(angle),
      dy: -ballSpeed * Math.cos(angle)
    }));
    setGameState('playing');
  };

  // Next level
  const nextLevel = () => {
    setLevel(prev => prev + 1);
    setPaddle({
      x: GAME_WIDTH / 2 - PADDLE_WIDTH / 2,
      y: GAME_HEIGHT - (isMobile ? 40 : 60),
      width: PADDLE_WIDTH,
      height: PADDLE_HEIGHT
    });
    setBall({
      x: GAME_WIDTH / 2,
      y: GAME_HEIGHT - (isMobile ? 60 : 80),
      radius: BALL_RADIUS,
      dx: 0,
      dy: 0
    });
    setGameState('waiting');
  };

  // Activate power-up
  const activatePowerUp = (powerUpType) => {
    const powerUp = powerUpTypes[powerUpType];

    switch (powerUpType) {
      case 'BLASTER':
        setActivePowerUps(prev => ({
          ...prev,
          BLASTER: Date.now() + powerUp.duration
        }));
        break;

      case 'MULTI_BALL':
        // Create additional balls
        setBall(prevBall => {
          // Create two additional balls with different angles
          const newBalls = [];
          for (let i = 0; i < 2; i++) {
            const angle = (Math.random() - 0.5) * 1.2;
            newBalls.push({
              x: prevBall.x + (i - 0.5) * 20,
              y: prevBall.y,
              radius: BALL_RADIUS,
              dx: ballSpeed * Math.sin(angle),
              dy: -ballSpeed * Math.cos(angle),
              id: Date.now() + i
            });
          }
          // Note: This is simplified - full multi-ball would need array of balls
          return prevBall;
        });
        break;

      case 'BIG_PADDLE':
        setActivePowerUps(prev => ({
          ...prev,
          BIG_PADDLE: Date.now() + powerUp.duration
        }));
        break;

      case 'SLOW_BALL':
        setActivePowerUps(prev => ({
          ...prev,
          SLOW_BALL: Date.now() + powerUp.duration
        }));
        break;

      case 'EXTRA_LIFE':
        setLives(prev => prev + 1);
        break;
    }
  };

  // Create blaster shot
  const createBlasterShot = () => {
    if (activePowerUps.BLASTER && Date.now() < activePowerUps.BLASTER) {
      const newShot = {
        id: Date.now() + Math.random(),
        x: paddle.x + paddle.width / 2,
        y: paddle.y - 10,
        width: 4,
        height: 10,
        dy: -8, // Move upward
        active: true
      };
      setBlasterShots(prev => [...prev, newShot]);
    }
  };

  // Reset current level (when losing all lives)
  const resetCurrentLevel = () => {
    setPaddle({
      x: GAME_WIDTH / 2 - PADDLE_WIDTH / 2,
      y: GAME_HEIGHT - (isMobile ? 40 : 60),
      width: PADDLE_WIDTH,
      height: PADDLE_HEIGHT
    });
    setBall({
      x: GAME_WIDTH / 2,
      y: GAME_HEIGHT - (isMobile ? 60 : 80),
      radius: BALL_RADIUS,
      dx: 0,
      dy: 0
    });
    setLives(3);
    setPowerUps([]);
    setActivePowerUps({});
    setBlasterShots([]);
    setGameState('waiting');
    // Keep current level, score, and ball speed
  };

  // Reset entire game (complete restart)
  const resetGame = () => {
    // Check if this is a new high score (only when game is won or completed)
    const currentHighScore = parseInt(localStorage.getItem('brickBreakerHighScore') || '0');
    const isNewHigh = score > currentHighScore && (gameState === 'won' || gameState === 'gameOver');

    if (isNewHigh && score > 0) {
      setIsNewHighScore(true);
      setShowHighScoreInput(true);
      return; // Don't reset yet, wait for high score submission
    }

    // Perform the actual reset
    performGameReset();
  };

  const performGameReset = () => {
    setPaddle({
      x: GAME_WIDTH / 2 - PADDLE_WIDTH / 2,
      y: GAME_HEIGHT - (isMobile ? 40 : 60),
      width: PADDLE_WIDTH,
      height: PADDLE_HEIGHT
    });
    setBall({
      x: GAME_WIDTH / 2,
      y: GAME_HEIGHT - (isMobile ? 60 : 80),
      radius: BALL_RADIUS,
      dx: 0,
      dy: 0
    });
    setScore(0);
    setLives(3);
    setLevel(1);
    setBallSpeed(4);
    setPowerUps([]);
    setActivePowerUps({});
    setBlasterShots([]);
    setGameState('waiting');
    setIsNewHighScore(false);
  };

  const handleHighScoreSubmit = (result) => {
    console.log('High score submitted:', result);

    // Update local high score
    localStorage.setItem('brickBreakerHighScore', score.toString());

    setShowHighScoreInput(false);

    // Now reset the game
    performGameReset();
  };

  const handleHighScoreCancel = () => {
    setShowHighScoreInput(false);

    // Update local high score if needed
    const currentHighScore = parseInt(localStorage.getItem('brickBreakerHighScore') || '0');
    if (score > currentHighScore) {
      localStorage.setItem('brickBreakerHighScore', score.toString());
    }

    // Reset the game
    performGameReset();
  };

  // Collision detection
  const checkCollision = (rect1, rect2) => {
    return rect1.x < rect2.x + rect2.width &&
      rect1.x + rect1.width > rect2.x &&
      rect1.y < rect2.y + rect2.height &&
      rect1.y + rect1.height > rect2.y;
  };

  // Ball collision with circle
  const checkBallCollision = (ball, rect) => {
    const distX = Math.abs(ball.x - rect.x - rect.width / 2);
    const distY = Math.abs(ball.y - rect.y - rect.height / 2);

    if (distX > (rect.width / 2 + ball.radius)) return false;
    if (distY > (rect.height / 2 + ball.radius)) return false;

    if (distX <= (rect.width / 2)) return true;
    if (distY <= (rect.height / 2)) return true;

    const dx = distX - rect.width / 2;
    const dy = distY - rect.height / 2;
    return (dx * dx + dy * dy <= (ball.radius * ball.radius));
  };

  // Game loop
  const gameLoop = useCallback(() => {
    if (gameState !== 'playing') return;

    // Update power-up effects
    const currentTime = Date.now();
    const currentPaddleWidth = activePowerUps.BIG_PADDLE && currentTime < activePowerUps.BIG_PADDLE
      ? PADDLE_WIDTH * 1.5 : PADDLE_WIDTH;
    const currentBallSpeed = activePowerUps.SLOW_BALL && currentTime < activePowerUps.SLOW_BALL
      ? ballSpeed * 0.6 : ballSpeed;

    // Update paddle width if big paddle is active
    setPaddle(prev => ({
      ...prev,
      width: currentPaddleWidth
    }));

    // Update falling power-ups
    setPowerUps(prevPowerUps => {
      return prevPowerUps.map(powerUp => ({
        ...powerUp,
        y: powerUp.y + powerUp.dy
      })).filter(powerUp => {
        // Remove power-ups that fell off screen
        if (powerUp.y > GAME_HEIGHT) return false;

        // Check collision with paddle
        if (checkCollision(powerUp, paddle)) {
          activatePowerUp(powerUp.type);
          return false; // Remove collected power-up
        }

        return true;
      });
    });

    // Update blaster shots
    setBlasterShots(prevShots => {
      return prevShots.map(shot => ({
        ...shot,
        y: shot.y + shot.dy
      })).filter(shot => {
        // Remove shots that went off screen
        if (shot.y < 0) return false;

        // Check collision with bricks
        let hitBrick = false;
        setBricks(prevBricks => {
          const newBricks = [...prevBricks];
          for (let i = 0; i < newBricks.length; i++) {
            if (!newBricks[i].destroyed && checkCollision(shot, newBricks[i])) {
              newBricks[i].destroyed = true;
              hitBrick = true;
              setScore(prev => prev + (newBricks[i].points || 10));

              // Generate power-up
              const newPowerUp = generatePowerUp(newBricks[i].x, newBricks[i].y);
              if (newPowerUp) {
                setPowerUps(prev => [...prev, newPowerUp]);
              }
              break;
            }
          }
          return newBricks;
        });

        return !hitBrick; // Remove shot if it hit a brick
      });
    });

    setBall(prevBall => {
      let newBall = { ...prevBall };
      // Apply speed modifications
      const speedMultiplier = currentBallSpeed / ballSpeed;
      newBall.x += newBall.dx * speedMultiplier;
      newBall.y += newBall.dy * speedMultiplier;

      // Wall collisions
      if (newBall.x <= newBall.radius || newBall.x >= GAME_WIDTH - newBall.radius) {
        newBall.dx = -newBall.dx;
      }
      if (newBall.y <= newBall.radius) {
        newBall.dy = -newBall.dy;
      }

      // Bottom wall (lose life)
      if (newBall.y >= GAME_HEIGHT - newBall.radius) {
        // Immediately reset ball position to prevent multiple hits
        newBall = {
          x: GAME_WIDTH / 2,
          y: GAME_HEIGHT - (isMobile ? 60 : 80),
          radius: BALL_RADIUS,
          dx: 0,
          dy: 0
        };

        setLives(prev => {
          const newLives = prev - 1;
          if (newLives <= 0) {
            // Game over - trigger high score check immediately
            setGameState('gameOver');
            return 0; // Ensure lives don't go below 0
          } else {
            setGameState('waiting');
            return newLives;
          }
        });
      }

      // Paddle collision
      if (checkBallCollision(newBall, paddle)) {
        const paddleCenter = paddle.x + paddle.width / 2;
        const ballRelativePos = (newBall.x - paddleCenter) / (paddle.width / 2);
        newBall.dx = ballRelativePos * 5;
        newBall.dy = -Math.abs(newBall.dy);
      }

      // Brick collisions
      setBricks(prevBricks => {
        const newBricks = [...prevBricks];
        let brickHit = false;

        for (let i = 0; i < newBricks.length; i++) {
          if (!newBricks[i].destroyed && checkBallCollision(newBall, newBricks[i])) {
            newBricks[i].destroyed = true;
            newBall.dy = -newBall.dy;
            brickHit = true;
            setScore(prev => prev + (newBricks[i].points || 10));

            // Generate power-up
            const newPowerUp = generatePowerUp(newBricks[i].x, newBricks[i].y);
            if (newPowerUp) {
              setPowerUps(prev => [...prev, newPowerUp]);
            }
            break;
          }
        }

        // Check level completion
        const remainingBricks = newBricks.filter(brick => !brick.destroyed);
        if (remainingBricks.length === 0) {
          if (level >= 15) {
            setGameState('won'); // Game completed
          } else {
            setGameState('levelComplete');
          }
        }

        return newBricks;
      });

      return newBall;
    });
  }, [gameState, paddle]);

  // Clean up expired power-ups
  useEffect(() => {
    const interval = setInterval(() => {
      const currentTime = Date.now();
      setActivePowerUps(prev => {
        const updated = {};
        Object.entries(prev).forEach(([type, endTime]) => {
          if (currentTime < endTime) {
            updated[type] = endTime;
          }
        });
        return updated;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Animation loop
  useEffect(() => {
    const animate = () => {
      gameLoop();
      animationRef.current = requestAnimationFrame(animate);
    };

    if (gameState === 'playing') {
      animationRef.current = requestAnimationFrame(animate);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [gameLoop, gameState]);

  // Mouse/Touch movement for paddle
  const handlePointerMove = (e) => {
    if (gameState === 'playing' || gameState === 'waiting') {
      const stage = stageRef.current;
      const pointerPos = stage.getPointerPosition();
      if (pointerPos) {
        const newX = Math.max(0, Math.min(GAME_WIDTH - PADDLE_WIDTH, pointerPos.x - PADDLE_WIDTH / 2));
        setPaddle(prev => ({ ...prev, x: newX }));

        // Move ball with paddle when waiting
        if (gameState === 'waiting') {
          setBall(prev => ({ ...prev, x: newX + PADDLE_WIDTH / 2 }));
        }
      }
    }
  };

  // Touch-specific handlers for mobile
  const handleTouchMove = (e) => {
    e.evt.preventDefault(); // Prevent scrolling
    handlePointerMove(e);
  };

  // Keyboard handler
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Always prevent spacebar default behavior to stop page scrolling
      if (e.key === ' ') {
        e.preventDefault();
        if (gameState === 'playing') {
          createBlasterShot();
        }
      } else if (e.key.toLowerCase() === 'r' && (gameState === 'waiting' || gameState === 'playing')) {
        e.preventDefault();
        resetGame();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [gameState, activePowerUps, paddle]);

  // Click to start
  const handleClick = () => {
    if (gameState === 'waiting') {
      startGame();
    } else if (gameState === 'levelComplete') {
      nextLevel();
    } else if (gameState === 'gameOver' || gameState === 'won') {
      resetGame();
    }
  };

  // Handle shoot button for mobile
  const handleShoot = (e) => {
    e.stopPropagation();
    createBlasterShot();
  };

  return (
    <div
      style={{ textAlign: 'center', padding: isMobile ? '10px' : '20px' }}
      onKeyDown={(e) => {
        // Prevent spacebar from scrolling when game container is focused
        if (e.key === ' ') {
          e.preventDefault();
        }
      }}
      tabIndex={-1} // Make container focusable but not in tab order
    >
      <div style={{
        marginBottom: '10px',
        fontSize: isMobile ? '14px' : '16px',
        display: 'flex',
        justifyContent: 'center',
        flexWrap: 'wrap',
        gap: isMobile ? '10px' : '20px'
      }}>
        <span>Score: {score}</span>
        <span>Lives: {lives}</span>
        <span>Level: {level}</span>
      </div>

      {/* Active Power-ups Display - Fixed height container to prevent layout shift */}
      <div style={{
        marginBottom: '10px',
        fontSize: isMobile ? '12px' : '14px',
        display: 'flex',
        justifyContent: 'center',
        flexWrap: 'wrap',
        gap: '10px',
        minHeight: isMobile ? '20px' : '24px', // Reserve space even when empty
        alignItems: 'center'
      }}>
        {Object.keys(activePowerUps).length > 0 && Object.entries(activePowerUps).map(([type, endTime]) => {
          const powerUp = powerUpTypes[type];
          const timeLeft = Math.max(0, Math.ceil((endTime - Date.now()) / 1000));
          if (timeLeft <= 0 && powerUp.duration > 0) return null;

          return (
            <span key={type} style={{
              color: powerUp.color,
              fontWeight: 'bold',
              background: 'rgba(255,255,255,0.1)',
              padding: '2px 6px',
              borderRadius: '4px'
            }}>
              {powerUp.symbol} {powerUp.name} {powerUp.duration > 0 ? `(${timeLeft}s)` : ''}
            </span>
          );
        })}
      </div>

      <div style={{
        marginBottom: '10px',
        fontSize: isMobile ? '12px' : '14px',
        minHeight: isMobile ? '20px' : '24px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        {gameState === 'waiting' && <span>{isMobile ? 'Tap' : 'Click'} to start Level {level}! {!isMobile && '(Press R to restart from Level 1)'}</span>}
        {gameState === 'levelComplete' && <span style={{ color: 'green' }}>Level {level} Complete! {isMobile ? 'Tap' : 'Click'} for Level {level + 1}</span>}
        {gameState === 'gameOver' && <span style={{ color: 'red' }}>Game Over! {isMobile ? 'Tap' : 'Click'} to restart from Level 1</span>}
        {gameState === 'won' && <span style={{ color: 'gold' }}>🎉 All Levels Complete! You're a Brick Breaker Master! {isMobile ? 'Tap' : 'Click'} to play again</span>}
        {gameState === 'playing' && !isMobile && (
          <span style={{ color: '#ccc', fontSize: '12px' }}>
            Press R to restart | {activePowerUps.BLASTER && Date.now() < activePowerUps.BLASTER ? 'SPACEBAR to shoot!' : 'Collect power-ups!'}
          </span>
        )}
      </div>

      {/* High Scores Button - Always visible */}
      <div style={{
        marginBottom: '10px',
        height: isMobile ? '40px' : '44px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <button
          onClick={() => setShowHighScoreBoard(true)}
          style={{
            padding: isMobile ? '8px 16px' : '10px 20px',
            fontSize: isMobile ? '14px' : '16px',
            backgroundColor: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            touchAction: 'manipulation'
          }}
        >
          🏆 High Scores
        </button>
      </div>

      {/* Mobile Shoot Button */}
      {isMobile && gameState === 'playing' && activePowerUps.BLASTER && Date.now() < activePowerUps.BLASTER && (
        <div style={{ marginBottom: '10px' }}>
          <button
            onClick={handleShoot}
            style={{
              padding: '10px 20px',
              fontSize: '16px',
              backgroundColor: '#FF4444',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            🔫 SHOOT
          </button>
        </div>
      )}

      <Stage
        width={GAME_WIDTH}
        height={GAME_HEIGHT}
        ref={stageRef}
        onMouseMove={handlePointerMove}
        onTouchMove={handleTouchMove}
        onTouchStart={handlePointerMove}
        onClick={handleClick}
        onTap={handleClick}
        style={{
          border: '2px solid #333',
          cursor: isMobile ? 'default' : 'none',
          touchAction: 'none',
          userSelect: 'none',
          outline: 'none' // Remove focus outline
        }}
        tabIndex={0} // Make stage focusable for keyboard events
      >
        <Layer>
          {/* Background */}
          <Rect width={GAME_WIDTH} height={GAME_HEIGHT} fill="#1a1a2e" />

          {/* Bricks */}
          {bricks.map(brick => (
            !brick.destroyed && (
              <Rect
                key={brick.id}
                x={brick.x}
                y={brick.y}
                width={brick.width}
                height={brick.height}
                fill={brick.color}
                stroke="#fff"
                strokeWidth={1}
                cornerRadius={3}
              />
            )
          ))}

          {/* Paddle */}
          <Rect
            x={paddle.x}
            y={paddle.y}
            width={paddle.width}
            height={paddle.height}
            fill="#fff"
            cornerRadius={7}
          />

          {/* Ball */}
          <Circle
            x={ball.x}
            y={ball.y}
            radius={ball.radius}
            fill="#fff"
          />

          {/* Power-ups */}
          {powerUps.map(powerUp => (
            <Rect
              key={powerUp.id}
              x={powerUp.x}
              y={powerUp.y}
              width={powerUp.width}
              height={powerUp.height}
              fill={powerUpTypes[powerUp.type].color}
              stroke="#fff"
              strokeWidth={1}
              cornerRadius={5}
            />
          ))}

          {/* Power-up symbols */}
          {powerUps.map(powerUp => (
            <Text
              key={`symbol-${powerUp.id}`}
              x={powerUp.x + powerUp.width / 2}
              y={powerUp.y + powerUp.height / 2}
              text={powerUpTypes[powerUp.type].symbol}
              fontSize={12}
              fill="#fff"
              align="center"
              offsetX={6}
              offsetY={6}
            />
          ))}

          {/* Blaster shots */}
          {blasterShots.map(shot => (
            <Rect
              key={shot.id}
              x={shot.x}
              y={shot.y}
              width={shot.width}
              height={shot.height}
              fill="#FFFF00"
              cornerRadius={2}
            />
          ))}

          {/* Instructions */}
          {gameState === 'waiting' && (
            <Text
              x={GAME_WIDTH / 2}
              y={GAME_HEIGHT / 2}
              text={`Level ${level}\n${isMobile ? 'Touch screen to control paddle' : 'Move mouse to control paddle'}\n${isMobile ? 'Tap' : 'Click'} to launch ball`}
              fontSize={isMobile ? 16 : 20}
              fill="#fff"
              align="center"
              offsetX={isMobile ? 80 : 100}
            />
          )}

          {/* Level Complete Message */}
          {gameState === 'levelComplete' && (
            <Text
              x={GAME_WIDTH / 2}
              y={GAME_HEIGHT / 2}
              text={`🎉 Level ${level} Complete! 🎉\n${isMobile ? 'Tap' : 'Click'} to continue to Level ${level + 1}`}
              fontSize={isMobile ? 18 : 24}
              fill="#FFD700"
              align="center"
              offsetX={isMobile ? 100 : 150}
            />
          )}
        </Layer>
      </Stage>

      {/* High Score Input Modal */}
      {showHighScoreInput && (
        <HighScoreInput
          score={score}
          game="brick-breaker"
          onSubmit={handleHighScoreSubmit}
          onCancel={handleHighScoreCancel}
        />
      )}

      {/* High Score Board Modal */}
      {showHighScoreBoard && (
        <HighScoreBoard
          game="brick-breaker"
          isVisible={showHighScoreBoard}
          onClose={() => setShowHighScoreBoard(false)}
        />
      )}
    </div>
  );
};

export default BrickBreakerGame;
